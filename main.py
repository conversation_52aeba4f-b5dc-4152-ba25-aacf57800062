# 导入必要的库
import os  # 用于文件和目录操作
import re  # 用于正则表达式操作
import json  # 用于JSON数据处理
import argparse  # 用于命令行参数解析
import random  # 用于随机数生成
from tqdm import tqdm  # 用于显示进度条
import sys  # 用于系统相关操作
import yaml  # 用于YAML配置文件解析
from agents.multi_retrieval_agents import MRetrievalAgent  # 导入自定义的多重检索代理类

# 导入本地模型相关库
from langchain_community.llms.ollama import Ollama  # 导入Ollama语言模型

# 设置代理环境变量
import os
os.environ["http_proxy"] = "http://127.0.0.1:11434"
os.environ["https_proxy"] = "http://127.0.0.1:11434"

def load_config(config_file=None):
    """
    加载配置文件
    """
    if config_file and os.path.exists(config_file):
        with open(config_file, 'r', encoding='utf-8') as f:
            return yaml.safe_load(f)
    return {}

def parse_args():
    """
    解析命令行参数
    返回: 解析后的参数对象
    """
    parser = argparse.ArgumentParser()

    # 配置文件参数
    parser.add_argument('--config', type=str, default='config_cn.yaml', help='配置文件路径')

    # 数据相关参数
    parser.add_argument('--data_root', type=str)  # 数据根目录
    parser.add_argument('--image_root', type=str)  # 图像根目录
    parser.add_argument('--output_root', type=str)  # 输出根目录
    parser.add_argument('--caption_file', type=str)  # 图像描述文件
    parser.add_argument('--model', type=str, default='deepseek')  # 使用的模型
    parser.add_argument('--options', type=list, default=["A", "B", "C", "D", "E"])  # 选项列表
    
    # 用户选项
    parser.add_argument('--test_split', type=str, default='test', choices=['test', 'val', 'minival'])  # 测试集划分
    parser.add_argument('--prompt_format',  # 提示格式
                        type=str,
                        default='CQM-A',
                        choices=[
                            'CQM-A', 'CQM-LA', 'CQM-EA', 'CQM-LEA', 'CQM-ELA', 'CQM-AL', 'CQM-AE', 'CQM-ALE', 'QCM-A',
                            'QCM-LA', 'QCM-EA', 'QCM-LEA', 'QCM-ELA', 'QCM-AL', 'QCM-AE', 'QCM-ALE', 'QCML-A', 'QCME-A',
                            'QCMLE-A', 'QCLM-A', 'QCEM-A', 'QCLEM-A', 'QCML-AE'
                        ],
                        help='prompt format template')
    
    # 向量检索设置
    parser.add_argument('--working_dir', type=str)  # 工作目录
    parser.add_argument('--llm_model_name', type=str, default='deepseek-chat')  # LLM模型名称，改为DeepSeek
    parser.add_argument('--mode', type=str, default='hybrid')  # 运行模式
    parser.add_argument('--elasticsearch_api_key', type=str,
                        default='WjNaVEFwZ0JXdkp1N2ZFcy00ZE86Z2pqbDZwS2ZTMDRxLTYzZGxvQnJjQQ==')  # Elasticsearch API密钥
    parser.add_argument('--top_k', type=int, default=4)  # 检索的top-k结果

    # 本地模型设置（替代OpenAI）
    parser.add_argument('--local_model_name', type=str, default='deepseek-chat')  # 本地模型名称
    parser.add_argument('--local_model_base_url', type=str, default='http://localhost:11434')  # 本地模型服务地址
    parser.add_argument('--temperature', type=float, default=0.0)  # 温度参数
    parser.add_argument('--max_tokens',  # 最大token数
                        type=int,
                        default=512,
                        help='The maximum number of tokens allowed for the generated answer.')

    # 添加一些缺失的参数
    parser.add_argument('--test_number', type=int, default=-1, help='测试问题数量，-1表示全部')
    parser.add_argument('--debug', action='store_true', help='调试模式')
    parser.add_argument('--save_every', type=int, default=10, help='每隔多少个问题保存一次结果')
    parser.add_argument('--seed', type=int, default=42, help='随机种子')
    parser.add_argument('--label', type=str, default='hmrag_exp', help='实验标签')
    parser.add_argument('--shot_number', type=int, default=3, help='few-shot示例数量')
    parser.add_argument('--shot_qids', type=list, default=None, help='指定的示例问题ID')

    args = parser.parse_args()

    # 加载配置文件
    config = load_config(args.config)

    # 用配置文件中的值填充未指定的参数
    for key, value in config.items():
        if not hasattr(args, key) or getattr(args, key) is None:
            setattr(args, key, value)

    return args

def load_data(args):
    """
    加载数据
    参数:
        args: 命令行参数对象
    返回:
        problems: 问题数据
        qids: 测试问题ID列表
        shot_qids: 示例问题ID列表
    """
    # 加载问题数据
    problems = json.load(open(os.path.join(args.data_root, 'problems.json')))
    pid_splits = json.load(open(os.path.join(args.data_root, 'pid_splits.json')))
    captions = json.load(open(args.caption_file))["captions"]

    # 为每个问题添加图像描述
    for qid in problems:
        problems[qid]['caption'] = captions[qid] if qid in captions else ""

    # 获取测试集问题ID
    qids = pid_splits['%s' % (args.test_split)]
    qids = qids[:args.test_number] if args.test_number > 0 else qids
    print(f"number of test problems: {len(qids)}\n")

    # 从训练集中选择示例
    shot_qids = args.shot_qids
    train_qids = pid_splits['train']
    if shot_qids == None:
        assert args.shot_number >= 0 and args.shot_number <= 32
        shot_qids = random.sample(train_qids, args.shot_number)  # 随机采样
    else:
        shot_qids = [str(qid) for qid in shot_qids]
        for qid in shot_qids:
            assert qid in train_qids  # 检查shot_qids是否有效
    print("training question ids for prompting: ", shot_qids, "\n")

    return problems, qids, shot_qids

def main():
    """
    主函数：执行整个推理过程
    """
    # 解析命令行参数
    args = parse_args()
    print('====Input Arguments====')
    print(json.dumps(vars(args), indent=2, sort_keys=False))

    # 设置随机种子
    random.seed(args.seed)

    # 加载数据
    problems, qids, shot_qids = load_data(args)

    # 设置输出文件路径
    result_file = args.output_root + '/' + args.label + '_' + args.test_split + '.json'
    if not os.path.exists(args.output_root):
        os.makedirs(args.output_root)

    # 初始化检索代理
    sum_agent = MRetrievalAgent(args)
    correct = 0
    results = {}
    outputs = {}

    failed = []
    # 遍历测试问题
    for i, qid in enumerate(qids):
        if args.debug and i > 10:  # 调试模式下只处理前10个问题
            break
        if args.test_number > 0 and i >= args.test_number:  # 如果设置了测试数量限制
            break

        problem = problems[qid]
        answer = problem['answer']
        
        # 使用代理进行预测
        final_ans, all_messages = sum_agent.predict(problems, shot_qids, qid)
        outputs[qid] = all_messages
        results[qid] = final_ans
        
        # 统计正确率
        if final_ans == answer:
            correct += 1
        else:
            failed.append(qid)
            
        # 定期保存结果
        if (i + 1) % args.save_every == 0:
            with open(result_file, 'w') as f:
                json.dump(results, f, indent=2)
            print(f"Results saved to {result_file} after {i + 1} examples.")

    # 保存最终结果
    with open(result_file, 'w') as f:
        json.dump(results, f, indent=2)
    print(f"Results saved to {result_file} after {len(qids)} examples.")
    print(f"Number of correct answers: {correct}/{len(qids)}")
    print(f"Accuracy: {correct / len(qids):.4f}")
    print(f"Failed question ids: {failed}")
    print(f"Number of failed questions: {len(failed)}")

if __name__ == "__main__":
    main()


