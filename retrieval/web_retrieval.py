
from elasticsearch import Elasticsearch
from transformers import AutoTokenizer, AutoModelForCausalLM
from langchain_community.llms import Ollama
import requests
import json

from retrieval.base_retrieval import BaseRetrieval

class WebRetrieval(BaseRetrieval):
    def __init__(self, config):
        self.config = config
        self.search_engine = "Elasticsearch"  # 使用Elasticsearch替代Google Serper

        # 初始化Elasticsearch客户端
        self.client = Elasticsearch(
            [{'host': 'localhost', 'port': 9200}],  # 本地Elasticsearch服务
            api_key=config.elasticsearch_api_key,  # 使用您提供的API密钥
            verify_certs=False,
            ssl_show_warn=False
        )

        self.generator = getattr(config, 'web_llm_model_name', 'deepseek-chat')

        # 使用本地部署的DeepSeek模型替代Ollama
        self.llm = Ollama(
            base_url="http://localhost:11434",  # 本地Ollama服务地址
            model="deepseek-chat",  # 使用DeepSeek模型
            temperature=0.35,
        )
        self.results = []

    def format_results(self, results):
        """Format the Elasticsearch search results"""
        max_results = 1
        processed = []

        if 'hits' in results and 'hits' in results['hits']:
            for hit in results['hits']['hits'][:max_results]:
                source = hit.get('_source', {})
                title = source.get('title', 'No title')
                content = source.get('content', source.get('text', 'No content'))
                url = source.get('url', source.get('link', ''))

                processed.append(f"[{title}]\n{content}\nLink:{url}\n")

        return "\n".join(processed) or "No relevant results found"
    
    def generation(self, results):
        # 使用 Ollama 模型生成回答
        answer = self.llm(results)
        return answer
        
    
    def elasticsearch_search(self, query, index_name="knowledge_base"):
        """使用Elasticsearch进行搜索"""
        try:
            search_body = {
                "query": {
                    "multi_match": {
                        "query": query,
                        "fields": ["title^2", "content", "text"],  # 标题权重更高
                        "type": "best_fields",
                        "fuzziness": "AUTO"
                    }
                },
                "size": self.config.top_k,
                "highlight": {
                    "fields": {
                        "content": {},
                        "text": {},
                        "title": {}
                    }
                }
            }

            response = self.client.search(
                index=index_name,
                body=search_body
            )
            return response
        except Exception as e:
            print(f"Elasticsearch search error: {e}")
            return {"hits": {"hits": []}}

    def find_top_k(self, query):
        self.results = self.elasticsearch_search(query)
        self.results = self.format_results(self.results)
        self.results = self.generation(self.results + "\n" + query)
        return self.results
    
    