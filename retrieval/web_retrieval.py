
from elasticsearch import Elasticsearch
from transformers import AutoTokenizer, AutoModelForCausalLM
from langchain_community.llms import Ollama
import requests
import json

from retrieval.base_retrieval import BaseRetrieval

class WebRetrieval(BaseRetrieval):
    def __init__(self, config):
        self.config = config
        self.search_engine = "Elasticsearch"  # 使用Elasticsearch替代Google Serper

        # 初始化Elasticsearch客户端
        self.client = Elasticsearch(
            [{'host': 'localhost', 'port': 9200}],  # 本地Elasticsearch服务
            api_key=config.elasticsearch_api_key,  # 使用您提供的API密钥
            verify_certs=False,
            ssl_show_warn=False
        )

        self.generator = getattr(config, 'web_llm_model_name', 'DeepSeek-V3')

        # 使用DeepSeek-V3 API替代本地Ollama
        self.deepseek_api_url = "https://jiutian.10086.cn/kunlun/ingress/api/h3t-dfac2f/38d0302f928448409370c011844e0eaa/ai-dcc6d8f2c1d24e56b61bca8a3e8ec9d8/service-9eb1de0939eb4931b37af78a93fed659/v1/chat/completions"
        self.deepseek_api_key = "*******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************"
        self.results = []

    def format_results(self, results):
        """Format the Elasticsearch search results"""
        max_results = 1
        processed = []

        if 'hits' in results and 'hits' in results['hits']:
            for hit in results['hits']['hits'][:max_results]:
                source = hit.get('_source', {})
                title = source.get('title', 'No title')
                content = source.get('content', source.get('text', 'No content'))
                url = source.get('url', source.get('link', ''))

                processed.append(f"[{title}]\n{content}\nLink:{url}\n")

        return "\n".join(processed) or "No relevant results found"
    
    def generation(self, results):
        # 使用 DeepSeek-V3 API 生成回答
        headers = {
            "Authorization": f"Bearer {self.deepseek_api_key}",
            "Content-Type": "application/json"
        }
        data = {
            "model": "DeepSeek-V3",
            "messages": [{"role": "user", "content": results}],
            "max_tokens": 4000,
            "temperature": 0.35
        }

        try:
            response = requests.post(self.deepseek_api_url, headers=headers, json=data, timeout=60)
            response.raise_for_status()
            result = response.json()
            return result["choices"][0]["message"]["content"]
        except Exception as e:
            print(f"DeepSeek-V3 API调用失败: {e}")
            return "API调用失败"
        
    
    def elasticsearch_search(self, query, index_name="knowledge_base"):
        """使用Elasticsearch进行搜索"""
        try:
            search_body = {
                "query": {
                    "multi_match": {
                        "query": query,
                        "fields": ["title^2", "content", "text"],  # 标题权重更高
                        "type": "best_fields",
                        "fuzziness": "AUTO"
                    }
                },
                "size": self.config.top_k,
                "highlight": {
                    "fields": {
                        "content": {},
                        "text": {},
                        "title": {}
                    }
                }
            }

            response = self.client.search(
                index=index_name,
                body=search_body
            )
            return response
        except Exception as e:
            print(f"Elasticsearch search error: {e}")
            return {"hits": {"hits": []}}

    def find_top_k(self, query):
        self.results = self.elasticsearch_search(query)
        self.results = self.format_results(self.results)
        self.results = self.generation(self.results + "\n" + query)
        return self.results
    
    