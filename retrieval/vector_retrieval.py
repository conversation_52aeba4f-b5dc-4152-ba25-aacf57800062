from lightrag import LightRAG, QueryParam
from lightrag.llm.ollama import ollama_model_complete, ollama_embed
from lightrag.utils import EmbeddingFunc
import requests
import json
import numpy as np
from sentence_transformers import SentenceTransformer
import torch

from retrieval.base_retrieval import BaseRetrieval

import os
os.environ["http_proxy"] = "http://127.0.0.1:11434"
os.environ["https_proxy"] = "http://127.0.0.1:11434"

class VectorRetrieval(BaseRetrieval):
    def __init__(self, config):
        self.config = config

        # 初始化BGE模型用于embedding
        self.bge_model = SentenceTransformer('BAAI/bge-large-zh-v1.5')

        # 自定义DeepSeek-V3 LLM函数
        def deepseek_v3_complete(prompt, model_name, **kwargs):
            url = "https://jiutian.10086.cn/kunlun/ingress/api/h3t-dfac2f/38d0302f928448409370c011844e0eaa/ai-dcc6d8f2c1d24e56b61bca8a3e8ec9d8/service-9eb1de0939eb4931b37af78a93fed659/v1/chat/completions"
            headers = {
                "Authorization": "Bearer *******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************",
                "Content-Type": "application/json"
            }
            data = {
                "model": "DeepSeek-V3",
                "messages": [{"role": "user", "content": prompt}],
                "max_tokens": kwargs.get("max_tokens", 4000),
                "temperature": kwargs.get("temperature", 0.7)
            }

            try:
                response = requests.post(url, headers=headers, json=data, timeout=60)
                response.raise_for_status()
                result = response.json()
                return result["choices"][0]["message"]["content"]
            except Exception as e:
                print(f"DeepSeek-V3 API调用失败: {e}")
                return "API调用失败"

        # 自定义BGE embedding函数
        def bge_embed(texts):
            try:
                # 首先尝试使用本地模型服务
                response = requests.post(
                    "http://localhost:5000/embed/bge",
                    json={"texts": texts},
                    timeout=30
                )
                if response.status_code == 200:
                    result = response.json()
                    return result["embeddings"]
                else:
                    print(f"模型服务调用失败，使用本地模型: {response.status_code}")
                    # 回退到本地模型
                    embeddings = self.bge_model.encode(texts, convert_to_tensor=True)
                    return embeddings.cpu().numpy().tolist()
            except requests.exceptions.RequestException:
                print("模型服务不可用，使用本地模型")
                try:
                    # 回退到本地模型
                    embeddings = self.bge_model.encode(texts, convert_to_tensor=True)
                    return embeddings.cpu().numpy().tolist()
                except Exception as e:
                    print(f"BGE embedding失败: {e}")
                    # 返回默认维度的零向量
                    return [[0.0] * 1024 for _ in texts]
            except Exception as e:
                print(f"BGE embedding失败: {e}")
                # 返回默认维度的零向量
                return [[0.0] * 1024 for _ in texts]

        self.client = LightRAG(
        working_dir=self.config.working_dir,
        llm_model_func=deepseek_v3_complete,
        llm_model_name="DeepSeek-V3",
        llm_model_max_async=160,
        llm_model_max_token_size=65536,
        llm_model_kwargs={
            "max_tokens": 4000,
            "temperature": 0.7
        },
        embedding_func=EmbeddingFunc(
            embedding_dim=1024,  # BGE-large-zh-v1.5的维度
            max_token_size=8192,
            func=bge_embed,
        ),
        )
        self.results = []

    
    def find_top_k(self, query):
        # self.results = self.client.query(query, 
                                        #  param=QueryParam(mode=self.config.mode, 
                                        #                   top_k=self.config.top_k),
                                        #  param=QueryParam(mode="naive"))
        prompt = "Context: N/A\nQuestion: Which figure of speech is used in this text?\nSing, O goddess, the anger of Achilles son of Peleus, that brought countless ills upon the Achaeans.\n—Homer, The Iliad\nOptions: (A) chiasmus (B) apostrophe\nAnswer:\nSummary the output with format 'Answer: The answer is A, B, C, D, E or FAILED. \n BECAUSE: '"
        self.results = self.client.query(prompt, 
                                        #  param=QueryParam(mode=self.config.mode))
                                                        #  , top_k=self.config.top_k))
                                         param=QueryParam(mode="naive"))
        return self.results
    
    