#!/usr/bin/env python3
"""
快速修复模型部署问题
使用可用的替代模型
"""

import os
import sys
from sentence_transformers import SentenceTransformer

def test_and_save_working_models():
    """测试并保存可用的模型"""
    print("快速修复模型部署问题")
    print("=" * 50)
    
    # 测试BGE类型的模型
    bge_models = [
        'sentence-transformers/paraphrase-multilingual-MiniLM-L12-v2',
        'sentence-transformers/distiluse-base-multilingual-cased',
        'sentence-transformers/all-MiniLM-L12-v2'
    ]
    
    print("测试BGE类型模型...")
    bge_success = False
    for model_name in bge_models:
        try:
            print(f"尝试: {model_name}")
            model = SentenceTransformer(model_name)
            
            # 测试编码
            test_texts = ["测试文本", "test text"]
            embeddings = model.encode(test_texts)
            
            print(f"✓ 成功! 维度: {embeddings.shape[1]}")
            
            # 保存成功的模型名称
            with open('bge_model_name.txt', 'w') as f:
                f.write(model_name)
            
            bge_success = True
            break
            
        except Exception as e:
            print(f"✗ 失败: {e}")
            continue
    
    # 测试CLIP类型的模型
    clip_models = [
        'sentence-transformers/all-MiniLM-L6-v2',
        'sentence-transformers/all-mpnet-base-v2',
        'sentence-transformers/paraphrase-MiniLM-L6-v2'
    ]
    
    print("\n测试CLIP类型模型...")
    clip_success = False
    for model_name in clip_models:
        try:
            print(f"尝试: {model_name}")
            model = SentenceTransformer(model_name)
            
            # 测试编码
            test_texts = ["test text", "another test"]
            embeddings = model.encode(test_texts)
            
            print(f"✓ 成功! 维度: {embeddings.shape[1]}")
            
            # 保存成功的模型名称
            with open('clip_model_name.txt', 'w') as f:
                f.write(model_name)
            
            clip_success = True
            break
            
        except Exception as e:
            print(f"✗ 失败: {e}")
            continue
    
    return bge_success and clip_success

def create_simple_model_service():
    """创建简化的模型服务"""
    
    # 读取可用的模型名称
    try:
        with open('bge_model_name.txt', 'r') as f:
            bge_model_name = f.read().strip()
    except:
        bge_model_name = 'sentence-transformers/all-MiniLM-L12-v2'
    
    try:
        with open('clip_model_name.txt', 'r') as f:
            clip_model_name = f.read().strip()
    except:
        clip_model_name = 'sentence-transformers/all-MiniLM-L6-v2'
    
    service_code = f'''#!/usr/bin/env python3
"""
简化的模型服务
使用可用的替代模型
"""

from flask import Flask, request, jsonify
from sentence_transformers import SentenceTransformer
import logging

app = Flask(__name__)
logging.basicConfig(level=logging.INFO)

print("正在加载模型...")
try:
    bge_model = SentenceTransformer('{bge_model_name}')
    print(f"✓ BGE模型加载成功: {bge_model_name}")
except Exception as e:
    print(f"✗ BGE模型加载失败: {{e}}")
    bge_model = None

try:
    clip_model = SentenceTransformer('{clip_model_name}')
    print(f"✓ CLIP模型加载成功: {clip_model_name}")
except Exception as e:
    print(f"✗ CLIP模型加载失败: {{e}}")
    clip_model = None

@app.route('/embed/bge', methods=['POST'])
def bge_embed():
    """BGE模型embedding接口"""
    if bge_model is None:
        return jsonify({{'error': 'BGE模型不可用'}}), 500
    
    try:
        data = request.json
        texts = data.get('texts', [])
        
        if not texts:
            return jsonify({{'error': '缺少texts参数'}}), 400
        
        embeddings = bge_model.encode(texts)
        return jsonify({{
            'embeddings': embeddings.tolist(),
            'dimension': embeddings.shape[1],
            'count': len(texts)
        }})
    except Exception as e:
        return jsonify({{'error': str(e)}}), 500

@app.route('/embed/clip', methods=['POST'])
def clip_embed():
    """CLIP模型embedding接口"""
    if clip_model is None:
        return jsonify({{'error': 'CLIP模型不可用'}}), 500
    
    try:
        data = request.json
        texts = data.get('texts', [])
        
        if not texts:
            return jsonify({{'error': '缺少texts参数'}}), 400
        
        embeddings = clip_model.encode(texts)
        return jsonify({{
            'embeddings': embeddings.tolist(),
            'dimension': embeddings.shape[1],
            'count': len(texts)
        }})
    except Exception as e:
        return jsonify({{'error': str(e)}}), 500

@app.route('/health', methods=['GET'])
def health():
    """健康检查接口"""
    models = []
    if bge_model is not None:
        models.append('{bge_model_name}')
    if clip_model is not None:
        models.append('{clip_model_name}')
    
    return jsonify({{'status': 'healthy', 'models': models}})

if __name__ == '__main__':
    print("启动模型服务...")
    print("BGE embedding API: http://localhost:5000/embed/bge")
    print("CLIP embedding API: http://localhost:5000/embed/clip")
    print("健康检查: http://localhost:5000/health")
    app.run(host='0.0.0.0', port=5000, debug=False)
'''
    
    with open('model_service.py', 'w', encoding='utf-8') as f:
        f.write(service_code)
    
    print("✓ 简化模型服务已创建")

def main():
    """主函数"""
    print("开始快速修复...")
    
    # 测试并保存可用模型
    if test_and_save_working_models():
        print("\n✓ 找到可用模型")
        
        # 创建模型服务
        create_simple_model_service()
        
        print("\n" + "=" * 50)
        print("快速修复完成!")
        print("=" * 50)
        print("下一步:")
        print("1. 启动模型服务: python model_service.py")
        print("2. 运行系统: python main.py --config config_cn.yaml")
        print("=" * 50)
        
        return 0
    else:
        print("\n✗ 无法找到可用模型")
        print("请检查网络连接或手动安装模型")
        return 1

if __name__ == "__main__":
    sys.exit(main())
