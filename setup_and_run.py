#!/usr/bin/env python3
"""
HM-RAG 一键部署和运行脚本
自动完成模型部署、服务启动和系统运行
"""

import os
import sys
import subprocess
import time
import signal
import threading
import requests
from pathlib import Path

class HMRAGSetup:
    def __init__(self):
        self.model_service_process = None
        self.setup_complete = False
        
    def check_dependencies(self):
        """检查依赖是否安装"""
        print("检查依赖...")
        
        required_packages = [
            'sentence_transformers',
            'flask', 
            'requests',
            'torch',
            'transformers'
        ]
        
        missing_packages = []
        for package in required_packages:
            try:
                __import__(package)
                print(f"✓ {package}")
            except ImportError:
                missing_packages.append(package)
                print(f"✗ {package}")
        
        if missing_packages:
            print(f"\n缺少依赖包: {', '.join(missing_packages)}")
            print("正在安装...")
            try:
                subprocess.check_call([
                    sys.executable, "-m", "pip", "install", 
                    "sentence-transformers", "flask", "requests"
                ])
                print("✓ 依赖安装完成")
            except subprocess.CalledProcessError:
                print("✗ 依赖安装失败")
                return False
        
        return True
    
    def deploy_models(self):
        """部署模型"""
        print("\n" + "=" * 50)
        print("部署模型")
        print("=" * 50)
        
        if os.path.exists("deploy_bge_model.py"):
            try:
                result = subprocess.run([sys.executable, "deploy_bge_model.py"], 
                                      capture_output=True, text=True, timeout=300)
                if result.returncode == 0:
                    print("✓ 模型部署成功")
                    return True
                else:
                    print(f"✗ 模型部署失败: {result.stderr}")
                    return False
            except subprocess.TimeoutExpired:
                print("✗ 模型部署超时")
                return False
            except Exception as e:
                print(f"✗ 模型部署异常: {e}")
                return False
        else:
            print("✗ 找不到模型部署脚本")
            return False
    
    def start_model_service(self):
        """启动模型服务"""
        print("\n" + "=" * 50)
        print("启动模型服务")
        print("=" * 50)
        
        if not os.path.exists("model_service.py"):
            print("✗ 找不到模型服务脚本")
            return False
        
        try:
            # 在后台启动模型服务
            self.model_service_process = subprocess.Popen([
                sys.executable, "model_service.py"
            ], stdout=subprocess.PIPE, stderr=subprocess.PIPE)
            
            print("正在启动模型服务...")
            
            # 等待服务启动
            for i in range(30):  # 最多等待30秒
                try:
                    response = requests.get("http://localhost:5000/health", timeout=2)
                    if response.status_code == 200:
                        print("✓ 模型服务启动成功")
                        return True
                except requests.exceptions.RequestException:
                    pass
                
                time.sleep(1)
                print(f"等待服务启动... ({i+1}/30)")
            
            print("✗ 模型服务启动超时")
            return False
            
        except Exception as e:
            print(f"✗ 模型服务启动失败: {e}")
            return False
    
    def test_integration(self):
        """测试集成"""
        print("\n" + "=" * 50)
        print("测试系统集成")
        print("=" * 50)
        
        if os.path.exists("test_model_integration.py"):
            try:
                result = subprocess.run([sys.executable, "test_model_integration.py"], 
                                      capture_output=True, text=True, timeout=120)
                if result.returncode == 0:
                    print("✓ 集成测试通过")
                    return True
                else:
                    print(f"✗ 集成测试失败")
                    print(result.stdout)
                    return False
            except subprocess.TimeoutExpired:
                print("✗ 集成测试超时")
                return False
            except Exception as e:
                print(f"✗ 集成测试异常: {e}")
                return False
        else:
            print("⚠ 跳过集成测试（找不到测试脚本）")
            return True
    
    def run_hmrag(self):
        """运行HM-RAG系统"""
        print("\n" + "=" * 50)
        print("启动HM-RAG系统")
        print("=" * 50)
        
        # 确保工作目录存在
        os.makedirs("working_dir", exist_ok=True)
        
        cmd = [
            sys.executable, "main.py",
            "--config", "config_cn.yaml",
            "--test_number", "3",  # 运行3个测试问题
            "--debug"
        ]
        
        print("运行命令:", " ".join(cmd))
        print("=" * 50)
        
        try:
            # 直接运行，显示输出
            subprocess.run(cmd)
        except KeyboardInterrupt:
            print("\n用户中断运行")
        except Exception as e:
            print(f"运行异常: {e}")
    
    def cleanup(self):
        """清理资源"""
        if self.model_service_process:
            print("\n正在关闭模型服务...")
            self.model_service_process.terminate()
            try:
                self.model_service_process.wait(timeout=5)
                print("✓ 模型服务已关闭")
            except subprocess.TimeoutExpired:
                self.model_service_process.kill()
                print("✓ 模型服务已强制关闭")
    
    def signal_handler(self, signum, frame):
        """信号处理器"""
        print(f"\n收到信号 {signum}，正在清理...")
        self.cleanup()
        sys.exit(0)
    
    def run(self):
        """主运行函数"""
        # 注册信号处理器
        signal.signal(signal.SIGINT, self.signal_handler)
        signal.signal(signal.SIGTERM, self.signal_handler)
        
        print("HM-RAG 自动部署和运行脚本")
        print("=" * 70)
        
        try:
            # 步骤1: 检查依赖
            if not self.check_dependencies():
                print("依赖检查失败，退出")
                return 1
            
            # 步骤2: 部署模型
            if not self.deploy_models():
                print("模型部署失败，退出")
                return 1
            
            # 步骤3: 启动模型服务
            if not self.start_model_service():
                print("模型服务启动失败，退出")
                return 1
            
            # 步骤4: 测试集成
            if not self.test_integration():
                print("集成测试失败，但继续运行...")
            
            # 步骤5: 运行HM-RAG
            self.setup_complete = True
            print("\n🎉 系统准备完成！")
            print("=" * 70)
            
            self.run_hmrag()
            
        except Exception as e:
            print(f"运行异常: {e}")
            return 1
        finally:
            self.cleanup()
        
        return 0

def main():
    """主函数"""
    setup = HMRAGSetup()
    return setup.run()

if __name__ == "__main__":
    sys.exit(main())
