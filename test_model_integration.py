#!/usr/bin/env python3
"""
模型集成测试脚本
测试DeepSeek-V3 API和本地embedding模型的集成
"""

import requests
import json
import time
import sys
from sentence_transformers import SentenceTransformer

def test_deepseek_v3_api():
    """测试DeepSeek-V3 API"""
    print("=" * 50)
    print("测试DeepSeek-V3 API")
    print("=" * 50)
    
    url = "https://jiutian.10086.cn/kunlun/ingress/api/h3t-dfac2f/38d0302f928448409370c011844e0eaa/ai-dcc6d8f2c1d24e56b61bca8a3e8ec9d8/service-9eb1de0939eb4931b37af78a93fed659/v1/chat/completions"
    headers = {
        "Authorization": "Bearer *******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************",
        "Content-Type": "application/json"
    }
    
    test_prompt = "请简单介绍一下人工智能的发展历史。"
    data = {
        "model": "DeepSeek-V3",
        "messages": [{"role": "user", "content": test_prompt}],
        "max_tokens": 500,
        "temperature": 0.7
    }
    
    try:
        print(f"发送请求: {test_prompt}")
        start_time = time.time()
        response = requests.post(url, headers=headers, json=data, timeout=60)
        end_time = time.time()
        
        if response.status_code == 200:
            result = response.json()
            answer = result["choices"][0]["message"]["content"]
            print(f"✓ API调用成功")
            print(f"✓ 响应时间: {end_time - start_time:.2f}秒")
            print(f"✓ 回答长度: {len(answer)}字符")
            print(f"✓ 回答预览: {answer[:100]}...")
            return True
        else:
            print(f"✗ API调用失败: {response.status_code}")
            print(f"✗ 错误信息: {response.text}")
            return False
            
    except Exception as e:
        print(f"✗ API调用异常: {e}")
        return False

def test_bge_model():
    """测试BGE模型"""
    print("\n" + "=" * 50)
    print("测试BGE模型")
    print("=" * 50)
    
    try:
        print("正在加载BGE模型...")
        model = SentenceTransformer('BAAI/bge-large-zh-v1.5')
        print("✓ BGE模型加载成功")
        
        test_texts = [
            "这是一个中文测试句子",
            "BGE模型用于文本嵌入",
            "人工智能技术发展迅速"
        ]
        
        print(f"测试文本: {test_texts}")
        start_time = time.time()
        embeddings = model.encode(test_texts)
        end_time = time.time()
        
        print(f"✓ 嵌入生成成功")
        print(f"✓ 处理时间: {end_time - start_time:.2f}秒")
        print(f"✓ 嵌入维度: {embeddings.shape[1]}")
        print(f"✓ 文本数量: {embeddings.shape[0]}")
        
        return True
        
    except Exception as e:
        print(f"✗ BGE模型测试失败: {e}")
        return False

def test_clip_model():
    """测试CLIP模型"""
    print("\n" + "=" * 50)
    print("测试CLIP模型")
    print("=" * 50)
    
    try:
        print("正在加载CLIP模型...")
        model = SentenceTransformer('clip-ViT-B-32')
        print("✓ CLIP模型加载成功")
        
        test_texts = [
            "A photo of a cat",
            "Image of a beautiful landscape",
            "Picture of a modern car"
        ]
        
        print(f"测试文本: {test_texts}")
        start_time = time.time()
        embeddings = model.encode(test_texts)
        end_time = time.time()
        
        print(f"✓ 嵌入生成成功")
        print(f"✓ 处理时间: {end_time - start_time:.2f}秒")
        print(f"✓ 嵌入维度: {embeddings.shape[1]}")
        print(f"✓ 文本数量: {embeddings.shape[0]}")
        
        return True
        
    except Exception as e:
        print(f"✗ CLIP模型测试失败: {e}")
        return False

def test_model_service():
    """测试本地模型服务"""
    print("\n" + "=" * 50)
    print("测试本地模型服务")
    print("=" * 50)
    
    # 测试健康检查
    try:
        response = requests.get("http://localhost:5000/health", timeout=5)
        if response.status_code == 200:
            print("✓ 模型服务运行正常")
            result = response.json()
            print(f"✓ 可用模型: {result.get('models', [])}")
        else:
            print("✗ 模型服务健康检查失败")
            return False
    except requests.exceptions.RequestException:
        print("⚠ 模型服务未启动，请先运行: python model_service.py")
        return False
    
    # 测试BGE embedding
    try:
        test_data = {"texts": ["测试BGE模型", "中文文本嵌入"]}
        response = requests.post("http://localhost:5000/embed/bge", json=test_data, timeout=10)
        if response.status_code == 200:
            result = response.json()
            print(f"✓ BGE服务正常，维度: {result['dimension']}")
        else:
            print("✗ BGE服务调用失败")
            return False
    except Exception as e:
        print(f"✗ BGE服务测试异常: {e}")
        return False
    
    # 测试CLIP embedding
    try:
        test_data = {"texts": ["test CLIP model", "English text embedding"]}
        response = requests.post("http://localhost:5000/embed/clip", json=test_data, timeout=10)
        if response.status_code == 200:
            result = response.json()
            print(f"✓ CLIP服务正常，维度: {result['dimension']}")
        else:
            print("✗ CLIP服务调用失败")
            return False
    except Exception as e:
        print(f"✗ CLIP服务测试异常: {e}")
        return False
    
    return True

def test_retrieval_integration():
    """测试检索模块集成"""
    print("\n" + "=" * 50)
    print("测试检索模块集成")
    print("=" * 50)
    
    try:
        # 创建简单的配置对象
        class SimpleConfig:
            def __init__(self):
                self.working_dir = "./test_working_dir"
        
        config = SimpleConfig()
        
        # 测试导入检索模块
        from retrieval.vector_retrieval import VectorRetrieval
        from retrieval.graph_retrieval import GraphRetrieval
        from retrieval.web_retrieval import WebRetrieval
        
        print("✓ 检索模块导入成功")
        
        # 注意：这里只测试导入，不实际初始化，因为需要完整的配置
        print("✓ 模块集成测试通过")
        return True
        
    except Exception as e:
        print(f"✗ 检索模块集成测试失败: {e}")
        return False

def main():
    """主测试函数"""
    print("HM-RAG 模型集成测试")
    print("=" * 70)
    
    test_results = []
    
    # 测试DeepSeek-V3 API
    test_results.append(("DeepSeek-V3 API", test_deepseek_v3_api()))
    
    # 测试BGE模型
    test_results.append(("BGE模型", test_bge_model()))
    
    # 测试CLIP模型
    test_results.append(("CLIP模型", test_clip_model()))
    
    # 测试模型服务
    test_results.append(("模型服务", test_model_service()))
    
    # 测试检索模块集成
    test_results.append(("检索模块集成", test_retrieval_integration()))
    
    # 输出测试结果
    print("\n" + "=" * 70)
    print("测试结果汇总")
    print("=" * 70)
    
    passed = 0
    total = len(test_results)
    
    for test_name, result in test_results:
        status = "✓ 通过" if result else "✗ 失败"
        print(f"{test_name:<20} {status}")
        if result:
            passed += 1
    
    print("=" * 70)
    print(f"总计: {passed}/{total} 项测试通过")
    
    if passed == total:
        print("🎉 所有测试通过！系统已准备就绪。")
        return 0
    else:
        print("⚠ 部分测试失败，请检查配置和依赖。")
        return 1

if __name__ == "__main__":
    sys.exit(main())
