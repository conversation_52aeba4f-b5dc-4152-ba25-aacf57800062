# 模型部署和配置指南

本指南说明如何按照您的要求配置HM-RAG系统中的模型。

## 修改内容总览

### 1. GraphRetrieval (graph_retrieval.py)
- **LLM模型**: 从本地DeepSeek → **DeepSeek-V3 API**
- **Embedding模型**: 从nomic-embed-text → **CLIP-ViT-B/32**

### 2. VectorRetrieval (vector_retrieval.py)  
- **LLM模型**: 从本地DeepSeek → **DeepSeek-V3 API**
- **Embedding模型**: 从nomic-embed-text → **BGE-large-zh-v1.5**

### 3. WebRetrieval (web_retrieval.py)
- **LLM模型**: 从本地Ollama → **DeepSeek-V3 API**

## 部署步骤

### 步骤1: 安装依赖
```bash
pip install -r requirements.txt
```

新增的依赖包括:
- `sentence-transformers`: 用于加载BGE和CLIP模型
- `flask`: 用于模型服务API
- `requests`: 用于API调用

### 步骤2: 部署本地模型
```bash
python deploy_bge_model.py
```

这个脚本会:
1. 下载BGE-large-zh-v1.5模型
2. 下载CLIP-ViT-B/32模型  
3. 创建本地模型服务脚本
4. 测试模型功能

### 步骤3: 启动模型服务
```bash
python model_service.py
```

模型服务提供以下API:
- BGE embedding: `http://localhost:5000/embed/bge`
- CLIP embedding: `http://localhost:5000/embed/clip`
- 健康检查: `http://localhost:5000/health`

### 步骤4: 测试模型服务
```bash
# 测试健康检查
curl http://localhost:5000/health

# 测试BGE模型
curl -X POST http://localhost:5000/embed/bge \
  -H "Content-Type: application/json" \
  -d '{"texts": ["测试文本"]}'

# 测试CLIP模型  
curl -X POST http://localhost:5000/embed/clip \
  -H "Content-Type: application/json" \
  -d '{"texts": ["test text"]}'
```

### 步骤5: 运行HM-RAG系统
```bash
python main.py --config config_cn.yaml
```

## API配置详情

### DeepSeek-V3 API配置
- **Base URL**: `https://jiutian.10086.cn/kunlun/ingress/api/h3t-dfac2f/38d0302f928448409370c011844e0eaa/ai-dcc6d8f2c1d24e56b61bca8a3e8ec9d8/service-9eb1de0939eb4931b37af78a93fed659/v1/chat/completions`
- **API Key**: `eyJ0eXAiOiJKV1QiLCJhbGciOiJSUzI1NiJ9.***********************************************************************************************************.dCcPDQiIKSor0qpYC2Cq1-gCP1jYFUA0HdG4-PBq87Xn8b5iQydSo1IR6qY0_1MIlNKCCW2g55NE9ofPbNjqY9167cw3pMULvTmgZZciP1qUeCVvv3C85QdtRmhcM7d9cl-Qq97-VCRvJm6A2JE8k3zx7RUCPsREHTOby8G9qbybLAQvvzZpOVKDkCQxYrpzPjLOTVtF-yzTknUzxCJ6agIyTeXmVktNf_8zj5P-pZ7G87x76v1rH2fLJ47-wFQnq5wHMmsZxkFqctmIKn89LIawMqAexY1uuR3kWjSno3JpQM_G898Qr88cO9uE3dUR4J68cjALro7yrL82Xmb5Yw`

### 模型维度信息
- **BGE-large-zh-v1.5**: 1024维
- **CLIP-ViT-B/32**: 512维

## 故障排除

### 1. 模型下载失败
如果模型下载失败，可能是网络问题。可以尝试:
```bash
# 设置镜像源
export HF_ENDPOINT=https://hf-mirror.com
python deploy_bge_model.py
```

### 2. GPU内存不足
如果GPU内存不足，模型会自动回退到CPU运行:
```python
# 在代码中已经处理了GPU/CPU自动切换
device = "cuda" if torch.cuda.is_available() else "cpu"
```

### 3. API调用失败
如果DeepSeek-V3 API调用失败，检查:
- 网络连接
- API密钥是否正确
- 请求频率是否过高

### 4. 模型服务不可用
系统已实现回退机制:
- 优先使用本地模型服务API
- 如果服务不可用，自动回退到直接调用模型

## 性能优化建议

### 1. GPU加速
确保安装了CUDA版本的PyTorch:
```bash
pip install torch torchvision torchaudio --index-url https://download.pytorch.org/whl/cu118
```

### 2. 批处理优化
模型服务支持批量处理文本，提高效率:
```python
# 批量处理多个文本
texts = ["文本1", "文本2", "文本3"]
embeddings = model.encode(texts)
```

### 3. 缓存机制
可以添加Redis缓存来存储常用的embedding结果。

## 验证修改

运行以下命令验证所有修改是否正确:
```bash
# 1. 检查模型服务
curl http://localhost:5000/health

# 2. 运行系统测试
python main.py --config config_cn.yaml --test_number 1 --debug

# 3. 检查日志输出
tail -f logs/hmrag.log
```

## 注意事项

1. **API密钥安全**: 请妥善保管DeepSeek-V3的API密钥
2. **模型大小**: BGE和CLIP模型总共约2-3GB，确保有足够存储空间
3. **网络要求**: 首次运行需要下载模型，确保网络稳定
4. **内存要求**: 建议至少8GB RAM用于模型推理
