name: hmrag
channels:
  - defaults
  - https://repo.anaconda.com/pkgs/main
  - https://repo.anaconda.com/pkgs/r
dependencies:
  - _libgcc_mutex=0.1=main
  - _openmp_mutex=5.1=1_gnu
  - bzip2=1.0.8=h5eee18b_6
  - ca-certificates=2025.2.25=h06a4308_0
  - ld_impl_linux-64=2.40=h12ee557_0
  - libffi=3.4.4=h6a678d5_1
  - libgcc-ng=11.2.0=h1234567_1
  - libgomp=11.2.0=h1234567_1
  - libstdcxx-ng=11.2.0=h1234567_1
  - libuuid=1.41.5=h5eee18b_0
  - ncurses=6.4=h6a678d5_0
  - openssl=3.0.16=h5eee18b_0
  - pip=25.0=py310h06a4308_0
  - python=3.10.16=he870216_1
  - readline=8.2=h5eee18b_0
  - setuptools=75.8.0=py310h06a4308_0
  - sqlite=3.45.3=h5eee18b_0
  - tk=8.6.14=h39e8969_0
  - wheel=0.45.1=py310h06a4308_0
  - xz=5.6.4=h5eee18b_1
  - zlib=1.2.13=h5eee18b_1
  - pip:
      - accelerate==1.5.2
      - aenum==3.1.15
      - aioboto3==14.1.0
      - aiobotocore==2.21.1
      - aiofiles==24.1.0
      - aiohappyeyeballs==2.6.1
      - aiohttp==3.11.13
      - aioitertools==0.12.0
      - aiosignal==1.3.2
      - annotated-types==0.7.0
      - anyio==4.8.0
      - anytree==2.12.1
      - asttokens==3.0.0
      - async-timeout==4.0.3
      - asyncpg==0.30.0
      - attrs==25.3.0
      - autograd==1.7.0
      - av==14.2.0
      - beartype==0.18.5
      - bitsandbytes==0.45.3
      - boto3==1.37.1
      - botocore==1.37.1
      - certifi==2025.1.31
      - cffi==1.17.1
      - charset-normalizer==3.4.1
      - click==8.1.8
      - clip-interrogator==0.6.0
      - contourpy==1.3.1
      - cryptography==44.0.2
      - cycler==0.12.1
      - decorator==5.2.1
      - deepspeed==0.16.4
      - distro==1.9.0
      - dnspython==2.7.0
      - einops==0.8.1
      - exceptiongroup==1.2.2
      - executing==2.2.0
      - filelock==3.18.0
      - fonttools==4.56.0
      - frozenlist==1.5.0
      - fsspec==2025.3.0
      - ftfy==6.3.1
      - gensim==4.3.3
      - graspologic==3.4.1
      - graspologic-native==1.2.3
      - greenlet==3.1.1
      - gremlinpython==3.7.3
      - grpcio==1.67.1
      - h11==0.14.0
      - hjson==3.1.0
      - hnswlib==0.8.0
      - httpcore==1.0.7
      - httpx==0.28.1
      - huggingface-hub==0.29.3
      - hyppo==0.4.0
      - idna==3.10
      - ipython==8.34.0
      - isodate==0.7.2
      - jedi==0.19.2
      - jinja2==3.1.6
      - jiter==0.9.0
      - jmespath==1.0.1
      - joblib==1.4.2
      - jsonpickle==4.0.2
      - kiwisolver==1.4.8
      - llvmlite==0.44.0
      - markupsafe==3.0.2
      - matplotlib==3.10.1
      - matplotlib-inline==0.1.7
      - milvus-lite==2.4.11
      - mpmath==1.3.0
      - msgpack==1.1.0
      - multidict==6.1.0
      - nano-vectordb==*******
      - neo4j==5.28.1
      - nest-asyncio==1.6.0
      - networkx==3.4.2
      - ninja==********
      - nltk==3.9.1
      - numba==0.61.0
      - numpy==1.26.4
      - nvidia-cublas-cu12==********
      - nvidia-cuda-cupti-cu12==12.4.127
      - nvidia-cuda-nvrtc-cu12==12.4.127
      - nvidia-cuda-runtime-cu12==12.4.127
      - nvidia-cudnn-cu12==********
      - nvidia-cufft-cu12==********
      - nvidia-curand-cu12==**********
      - nvidia-cusolver-cu12==********
      - nvidia-cusparse-cu12==**********
      - nvidia-cusparselt-cu12==0.6.2
      - nvidia-ml-py==12.570.86
      - nvidia-nccl-cu12==2.21.5
      - nvidia-nvjitlink-cu12==12.4.127
      - nvidia-nvtx-cu12==12.4.127
      - ollama==0.4.7
      - open-clip-torch==2.31.0
      - openai==1.66.3
      - opencv-python==*********
      - oracledb==3.0.0
      - packaging==24.2
      - pandas==2.2.3
      - parso==0.8.4
      - patsy==1.0.1
      - pexpect==4.9.0
      - pillow==11.1.0
      - pot==0.9.5
      - prompt-toolkit==3.0.50
      - propcache==0.3.0
      - protobuf==6.30.1
      - psutil==7.0.0
      - psycopg==3.2.6
      - psycopg-binary==3.2.6
      - psycopg-pool==3.2.6
      - ptyprocess==0.7.0
      - pure-eval==0.2.3
      - py-cpuinfo==9.0.0
      - pycparser==2.22
      - pydantic==2.10.6
      - pydantic-core==2.27.2
      - pygments==2.19.1
      - pymilvus==2.5.5
      - pymongo==4.11.2
      - pymysql==1.1.1
      - pynndescent==0.5.13
      - pyparsing==3.2.1
      - python-dateutil==2.9.0.post0
      - python-dotenv==1.0.1
      - pytz==2025.1
      - pyvis==0.3.2
      - pyyaml==6.0.2
      - qwen-vl-utils==0.0.10
      - regex==2024.11.6
      - requests==2.32.3
      - s3transfer==0.11.3
      - safetensors==0.5.3
      - scikit-learn==1.6.1
      - scipy==1.12.0
      - seaborn==0.13.2
      - six==1.17.0
      - smart-open==7.1.0
      - sniffio==1.3.1
      - sqlalchemy==2.0.39
      - stack-data==0.6.3
      - statsmodels==0.14.4
      - sympy==1.13.1
      - tenacity==9.0.0
      - termcolor==2.5.0
      - threadpoolctl==3.6.0
      - tiktoken==0.9.0
      - timm==1.0.15
      - tokenizers==0.21.1
      - torch==2.6.0
      - torchvision==0.21.0
      - tqdm==4.67.1
      - traitlets==5.14.3
      - transformers==4.49.0
      - triton==3.2.0
      - typing-extensions==4.12.2
      - tzdata==2025.1
      - ujson==5.10.0
      - umap-learn==0.5.7
      - urllib3==2.3.0
      - wcwidth==0.2.13
      - wrapt==1.17.2
      - xxhash==3.5.0
      - yarl==1.18.3
prefix: /root/miniconda3/envs/hmrag
