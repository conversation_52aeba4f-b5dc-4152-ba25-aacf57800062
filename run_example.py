#!/usr/bin/env python3
"""
HM-RAG 运行示例脚本
适配中国大陆环境，使用Elasticsearch和DeepSeek模型
"""

import subprocess
import sys
import os

def run_hmrag_example():
    """运行HM-RAG示例"""
    
    # 基本参数
    cmd = [
        "python", "main.py",
        "--working_dir", "./working_dir",
        "--elasticsearch_api_key", "WjNaVEFwZ0JXdkp1N2ZFcy00ZE86Z2pqbDZwS2ZTMDRxLTYzZGxvQnJjQQ==",
        "--local_model_name", "deepseek-chat",
        "--local_model_base_url", "http://localhost:11434",
        "--llm_model_name", "deepseek-chat",
        "--top_k", "4",
        "--temperature", "0.0",
        "--max_tokens", "512",
        "--test_number", "10",  # 测试10个问题
        "--debug",  # 启用调试模式
        "--save_every", "5",
        "--seed", "42",
        "--label", "hmrag_deepseek_test",
        "--shot_number", "3"
    ]
    
    # 如果有ScienceQA数据集，添加相关参数
    if os.path.exists("./ScienceQA/data"):
        cmd.extend([
            "--data_root", "./ScienceQA/data/scienceqa",
            "--image_root", "./ScienceQA/data/scienceqa/images",
            "--caption_file", "./ScienceQA/data/captions.json",
            "--output_root", "./results"
        ])
    
    print("运行命令:")
    print(" ".join(cmd))
    print("\n" + "="*50)
    
    try:
        # 运行命令
        result = subprocess.run(cmd, check=True, capture_output=True, text=True)
        print("运行成功!")
        print("输出:", result.stdout)
        if result.stderr:
            print("警告:", result.stderr)
    except subprocess.CalledProcessError as e:
        print(f"运行失败: {e}")
        print("错误输出:", e.stderr)
        print("标准输出:", e.stdout)
        return False
    
    return True

def check_prerequisites():
    """检查运行前提条件"""
    print("检查运行环境...")
    
    # 检查Ollama服务
    try:
        import requests
        response = requests.get("http://localhost:11434/api/tags", timeout=5)
        if response.status_code == 200:
            print("✓ Ollama服务运行正常")
            models = response.json().get('models', [])
            deepseek_found = any('deepseek' in model.get('name', '') for model in models)
            if deepseek_found:
                print("✓ 找到DeepSeek模型")
            else:
                print("⚠ 未找到DeepSeek模型，请确保已安装")
        else:
            print("✗ Ollama服务未响应")
            return False
    except Exception as e:
        print(f"✗ 无法连接到Ollama服务: {e}")
        print("请确保Ollama服务在http://localhost:11434运行")
        return False
    
    # 检查Elasticsearch（可选）
    try:
        import requests
        response = requests.get("http://localhost:9200", timeout=5)
        if response.status_code == 200:
            print("✓ Elasticsearch服务运行正常")
        else:
            print("⚠ Elasticsearch服务未响应，网络检索功能可能受限")
    except Exception as e:
        print(f"⚠ 无法连接到Elasticsearch: {e}")
        print("网络检索功能可能受限")
    
    # 检查工作目录
    if not os.path.exists("./working_dir"):
        os.makedirs("./working_dir")
        print("✓ 创建工作目录")
    
    if not os.path.exists("./results"):
        os.makedirs("./results")
        print("✓ 创建结果目录")
    
    return True

if __name__ == "__main__":
    print("HM-RAG 中国大陆适配版本运行脚本")
    print("="*50)
    
    if not check_prerequisites():
        print("环境检查失败，请解决上述问题后重试")
        sys.exit(1)
    
    print("\n开始运行HM-RAG...")
    success = run_hmrag_example()
    
    if success:
        print("\n运行完成! 结果保存在 ./results 目录中")
    else:
        print("\n运行失败，请检查错误信息")
        sys.exit(1)
