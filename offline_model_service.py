#!/usr/bin/env python3
"""
离线模型服务
使用简单的embedding方法，不依赖外部模型下载
"""

from flask import Flask, request, jsonify
import numpy as np
import hashlib
import logging
from sklearn.feature_extraction.text import TfidfVectorizer
import pickle
import os

app = Flask(__name__)
logging.basicConfig(level=logging.INFO)

class SimpleEmbedding:
    """简单的文本嵌入类"""
    
    def __init__(self, dimension=512):
        self.dimension = dimension
        self.vectorizer = TfidfVectorizer(max_features=dimension, stop_words='english')
        self.is_fitted = False
        
    def encode(self, texts):
        """编码文本为向量"""
        if isinstance(texts, str):
            texts = [texts]
        
        if not self.is_fitted:
            # 使用输入文本训练vectorizer
            try:
                self.vectorizer.fit(texts)
                self.is_fitted = True
            except:
                # 如果失败，使用简单的哈希方法
                return self._hash_encode(texts)
        
        try:
            # 使用TF-IDF向量化
            vectors = self.vectorizer.transform(texts).toarray()
            
            # 如果维度不足，用零填充
            if vectors.shape[1] < self.dimension:
                padding = np.zeros((vectors.shape[0], self.dimension - vectors.shape[1]))
                vectors = np.hstack([vectors, padding])
            elif vectors.shape[1] > self.dimension:
                vectors = vectors[:, :self.dimension]
                
            return vectors
        except:
            # 回退到哈希方法
            return self._hash_encode(texts)
    
    def _hash_encode(self, texts):
        """使用哈希的简单编码方法"""
        vectors = []
        for text in texts:
            # 使用MD5哈希生成固定长度的向量
            hash_obj = hashlib.md5(text.encode())
            hash_hex = hash_obj.hexdigest()
            
            # 将哈希转换为数字向量
            vector = []
            for i in range(0, len(hash_hex), 2):
                vector.append(int(hash_hex[i:i+2], 16) / 255.0)
            
            # 扩展到所需维度
            while len(vector) < self.dimension:
                vector.extend(vector[:min(len(vector), self.dimension - len(vector))])
            
            vectors.append(vector[:self.dimension])
        
        return np.array(vectors)

# 初始化模型
print("初始化离线embedding模型...")
bge_model = SimpleEmbedding(dimension=1024)  # BGE维度
clip_model = SimpleEmbedding(dimension=512)  # CLIP维度
print("✓ 离线模型初始化完成")

@app.route('/embed/bge', methods=['POST'])
def bge_embed():
    """BGE模型embedding接口"""
    try:
        data = request.json
        texts = data.get('texts', [])
        
        if not texts:
            return jsonify({'error': '缺少texts参数'}), 400
        
        embeddings = bge_model.encode(texts)
        return jsonify({
            'embeddings': embeddings.tolist(),
            'dimension': embeddings.shape[1],
            'count': len(texts)
        })
    except Exception as e:
        return jsonify({'error': str(e)}), 500

@app.route('/embed/clip', methods=['POST'])
def clip_embed():
    """CLIP模型embedding接口"""
    try:
        data = request.json
        texts = data.get('texts', [])
        
        if not texts:
            return jsonify({'error': '缺少texts参数'}), 400
        
        embeddings = clip_model.encode(texts)
        return jsonify({
            'embeddings': embeddings.tolist(),
            'dimension': embeddings.shape[1],
            'count': len(texts)
        })
    except Exception as e:
        return jsonify({'error': str(e)}), 500

@app.route('/health', methods=['GET'])
def health():
    """健康检查接口"""
    return jsonify({
        'status': 'healthy', 
        'models': ['offline-bge-1024d', 'offline-clip-512d'],
        'note': '使用离线embedding方法'
    })

@app.route('/test', methods=['GET'])
def test():
    """测试接口"""
    try:
        # 测试BGE
        test_texts = ["这是测试文本", "test text"]
        bge_embeddings = bge_model.encode(test_texts)
        
        # 测试CLIP
        clip_embeddings = clip_model.encode(test_texts)
        
        return jsonify({
            'bge_test': {
                'dimension': bge_embeddings.shape[1],
                'count': bge_embeddings.shape[0]
            },
            'clip_test': {
                'dimension': clip_embeddings.shape[1], 
                'count': clip_embeddings.shape[0]
            },
            'status': 'success'
        })
    except Exception as e:
        return jsonify({'error': str(e)}), 500

if __name__ == '__main__':
    print("启动离线模型服务...")
    print("BGE embedding API: http://localhost:5000/embed/bge")
    print("CLIP embedding API: http://localhost:5000/embed/clip")
    print("健康检查: http://localhost:5000/health")
    print("测试接口: http://localhost:5000/test")
    print("注意: 使用离线embedding方法，性能可能不如预训练模型")
    app.run(host='0.0.0.0', port=5000, debug=False)
