# HM-RAG 中国大陆适配配置文件
# 使用DeepSeek模型和Elasticsearch

# 模型配置
llm_model_name: "deepseek-chat"
local_model_name: "deepseek-chat"
local_model_base_url: "http://localhost:11434"

# 检索配置
top_k: 4
vector_search_mode: "naive"
graph_search_mode: "mix"

# Elasticsearch配置
elasticsearch_api_key: "WjNaVEFwZ0JXdkp1N2ZFcy00ZE86Z2pqbDZwS2ZTMDRxLTYzZGxvQnJjQQ=="
elasticsearch_host: "localhost"
elasticsearch_port: 9200

# 生成参数
temperature: 0.0
max_tokens: 512
max_intents: 3

# 实验配置
test_number: 10  # 测试问题数量，-1表示全部
debug: true
save_every: 5
seed: 42
label: "hmrag_deepseek_cn"
shot_number: 3

# 路径配置
working_dir: "./working_dir"
output_root: "./results"
data_root: "./ScienceQA/data/scienceqa"
image_root: "./ScienceQA/data/scienceqa/images"
caption_file: "./ScienceQA/data/captions.json"

# 提示模板配置
intent_count_prompt: |
  请计算以下查询中包含多少个独立的意图。
  只返回一个整数：
  {query}
  意图数量：

intent_split_prompt: |
  将以下查询分解为多个独立的子查询，用'||'分隔，不要额外的解释：
  {query}
  子查询列表：
