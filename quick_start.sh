#!/bin/bash

# HM-RAG 快速启动脚本 - 中国大陆适配版
# 使用DeepSeek模型和Elasticsearch

echo "HM-RAG 中国大陆适配版快速启动"
echo "================================"

# 检查Python环境
if ! command -v python &> /dev/null; then
    echo "错误: 未找到Python，请先安装Python"
    exit 1
fi

# 检查Ollama服务
echo "检查Ollama服务..."
if curl -s http://localhost:11434/api/tags > /dev/null; then
    echo "✓ Ollama服务运行正常"
else
    echo "✗ Ollama服务未运行，请先启动Ollama服务"
    echo "运行: ollama serve"
    exit 1
fi

# 检查DeepSeek模型
echo "检查DeepSeek模型..."
if ollama list | grep -q "deepseek-chat"; then
    echo "✓ DeepSeek模型已安装"
else
    echo "⚠ DeepSeek模型未安装，正在下载..."
    ollama pull deepseek-chat
    if [ $? -eq 0 ]; then
        echo "✓ DeepSeek模型下载完成"
    else
        echo "✗ DeepSeek模型下载失败"
        exit 1
    fi
fi

# 创建必要的目录
echo "创建工作目录..."
mkdir -p working_dir
mkdir -p results
echo "✓ 目录创建完成"

# 运行HM-RAG
echo "启动HM-RAG..."
echo "使用配置文件: config_cn.yaml"
echo "使用Elasticsearch API密钥: WjNaVEFwZ0JXdkp1N2ZFcy00ZE86Z2pqbDZwS2ZTMDRxLTYzZGxvQnJjQQ=="
echo ""

python main.py \
    --config config_cn.yaml \
    --working_dir ./working_dir \
    --elasticsearch_api_key WjNaVEFwZ0JXdkp1N2ZFcy00ZE86Z2pqbDZwS2ZTMDRxLTYzZGxvQnJjQQ== \
    --local_model_name deepseek-chat \
    --local_model_base_url http://localhost:11434 \
    --output_root ./results

echo ""
echo "运行完成！结果保存在 ./results 目录中"
